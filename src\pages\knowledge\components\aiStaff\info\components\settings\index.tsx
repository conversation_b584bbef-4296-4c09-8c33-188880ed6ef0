import React, { useState, useEffect, useCallback } from 'react';
import styles from './style/index.module.less';
import useLocale from '@/utils/useLocale';
import {
  Form,
  Image,
  Select,
  Message,
  Cascader,
  Modal,
  Button,
  Grid,
  Tooltip,
} from '@arco-design/web-react';
import IconSend from '@/assets/chat/send.svg';
import { useLocation } from 'react-router-dom';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import Text from '@arco-design/web-react/es/Typography/text';
import AddApplicationSettingIcon from '@/assets/application/addApplicationSetting.svg';
import ButtonComponent from '@arco-design/web-react/es/Button';
import WorkflowIcon from '@/assets/application/workflowIcon.png';
import AcpServerIcon from '@/assets/acp/acpServer.png';
import AcpServerIconSvg from '@/assets/acp/IconAcp.svg';
import AddIcon from '@/assets/application/addIcon.svg';
import IconCloseTag from '@/assets/close.svg';
import TreeModal from './components/TreeModal/TreeModal';
import {
  getLlmProviderNameList,
  getLlmProviderModelList,
} from '@/lib/services/llm-model-service';
import {
  AIPrompt,
  AgentResponse,
  getAgentList,
} from '@/lib/services/agent-service';
import { Input, message } from 'antd';
import { fetchKnowledgeCollections } from '@/pages/knowledge/components/knowledge/services/aiStaff-service';
import { listEmployeesall } from '@/pages/knowledge/components/knowledge/services/aiStaff-service';
const { TextArea } = Input;
const { Row, Col } = Grid;

import {
  Modal as ArcoModal,
  Input as ArcoInput,
  Tag,
} from '@arco-design/web-react';
import { IconSearch } from '@arco-design/web-react/icon';

const FormItem = Form.Item;
interface AiStaffSettingsProps {
  agentData: AgentResponse | null;
  loading: boolean;
  onAgentDataUpdate: (newData: Partial<AgentResponse>) => void;
  isEditing: boolean;
  newEmployeeData?: any | null;
}

interface WorkflowItem {
  id: string;
  name: string;
  description: string;
  createdTime: string;
  updatedTime: string;
  [key: string]: any;
}

interface KnowledgeItem {
  id: string;
  name: string;
  [key: string]: any;
}

interface UtilityItem {
  id: string;
  name: string;
  description: string;
  labels: string[];
  [key: string]: any;
}

interface AcpServerItem {
  id: string;
  name: string;
  description: string;
  createdTime: string;
  [key: string]: any;
}

interface DataValidationParam {
  required: boolean;
  field: string;
  type: string;
  description: string;
  redirect_to: string;
  field_type: string;
}

function AbilitySelectModal({ visible, onOk, onCancel, selected }) {
  const [search, setSearch] = useState('');
  const [hovered, setHovered] = useState('');
  const [loading, setLoading] = useState(false);
  const [employeeList, setEmployeeList] = useState([]);
  const [nameModalOpen, setNameModalOpen] = useState(false);
  const [pendingAbility, setPendingAbility] = useState(null);
  const [newAbilityName, setNewAbilityName] = useState('');

  useEffect(() => {
    if (!visible) return;
    setLoading(true);
    listEmployeesall({
      Pager: {
        Page: 1,
        Size: 20,
      },
    })
      .then((res) => {
        setEmployeeList(res?.data?.items || []);
        setLoading(false);
      })
      .catch(() => setLoading(false));
  }, [visible]);

  // 回车时才请求
  const handleSearch = () => {
    setLoading(true);
    listEmployeesall({
      Pager: {
        Page: 1,
        Size: 100,
      },
      Name: search,
    })
      .then((res) => {
        setEmployeeList(res?.data?.items || []);
        setLoading(false);
      })
      .catch(() => setLoading(false));
  };

  const handleUse = (item) => {
    setPendingAbility(item);
    setNewAbilityName('');
    setNameModalOpen(true);
  };
  const handleNameOk = () => {
    if (!newAbilityName.trim()) return;
    onOk({ ...pendingAbility, newAbilityName });
    setNameModalOpen(false);
  };

  return (
    <>
      <Modal
        title="选择员工能力"
        visible={visible}
        onCancel={onCancel}
        footer={null}
        style={{ width: 700 }}
      >
        <div style={{ display: 'flex', gap: 12, marginBottom: 16 }}>
          <Input
            prefix={<IconSearch />}
            placeholder="AI搜索..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onPressEnter={handleSearch}
            style={{ flex: 1 }}
          />
        </div>
        <div>
          {loading ? (
            <div>加载中...</div>
          ) : (
            employeeList.map((item) => (
              <div
                key={item.id}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  background: hovered === item.id ? '#f7f8fa' : '#fff',
                  borderRadius: 8,
                  marginBottom: 12,
                  padding: 16,
                }}
                onMouseEnter={() => setHovered(item.id)}
                onMouseLeave={() => setHovered('')}
              >
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 500, fontSize: 16 }}>
                    {item.name}
                  </div>
                  <div style={{ color: '#888', fontSize: 13, margin: '4px 0' }}>
                    {item.description}
                  </div>
                  <div style={{ marginTop: 4 }}>
                    {(item.labels || []).map((label, idx) => (
                      <Tag key={idx} style={{ marginRight: 4 }}>
                        {label}
                      </Tag>
                    ))}
                  </div>
                </div>
                <Button
                  type="primary"
                  size="small"
                  style={{ minWidth: 60 }}
                  onClick={() => handleUse(item)}
                  disabled={selected === item.id}
                >
                  {selected === item.id ? '已选择' : '使用'}
                </Button>
              </div>
            ))
          )}
        </div>
      </Modal>
      <ArcoModal
        title="输入新能力名称"
        visible={nameModalOpen}
        onOk={handleNameOk}
        onCancel={() => setNameModalOpen(false)}
        okButtonProps={{ disabled: !newAbilityName.trim() }}
      >
        <ArcoInput
          placeholder="请输入新能力名称"
          value={newAbilityName}
          onChange={(value) => setNewAbilityName(value)}
        />
      </ArcoModal>
    </>
  );
}

function AiStaffSettings({
  newEmployeeData,
  agentData,
  loading: parentLoading,
  onAgentDataUpdate,
  isEditing,
}: AiStaffSettingsProps) {
  // 智能体类型与配置项显示映射
  const agentTypeConfigMap = {
    routing: ['routingRule', 'responses', 'promptTemplate'],
    planning: ['routingRule', 'functions', 'responses', 'promptTemplate'],
    task: [
      'routingRule',
      'sequenceCard',
      'workflow',
      'knowledge',
      'tools',
      'acpServer',
      'functions',
      'responses',
      'promptTemplate',
    ],
    static: ['responses', 'promptTemplate'],
    workflow: ['sequenceCard', 'responses', 'promptTemplate'],
  };

  const [isInitialized, setIsInitialized] = useState(false);
  const locale = useLocale();
  const [form] = Form.useForm();

  const [taskAgentListFetched, setTaskAgentListFetched] = useState(false);
  const [loadingAgents, setLoadingAgents] = useState(false);

  // TreeModalData
  const [workflowData, setWorkflowData] = useState([]);
  const [knowledgeData, setKnowledgeData] = useState([]);
  const [utilityData, setUtilityData] = useState([]);
  const [acpServerData, setAcpServerData] = useState([]);
  const [treeData, setTreeData] = useState([]);

  const [loadingData, setLoadingData] = useState({
    workflow: false,
    knowledge: false,
    utility: false,
  });
  const Option = Select.Option;
  const [visibleTreeModal, setVisibleTreeModal] = useState(false);
  const [checkedIds, setCheckedIds] = useState([]);
  const [modalTitle, setModalTitle] = useState('');
  const [modalType, setModalType] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedModalValue, setSelectedModalValue] = useState<any[]>([]);
  const [aiAssistantVisible, setAiAssistantVisible] = useState(false);
  const [functionModalVisible, setFunctionModalVisible] = useState(false);
  const [promptTemplateModalVisible, setPromptTemplateModalVisible] =
    useState(false);
  const [responseModalVisible, setResponseModalVisible] = useState(false);
  const [acpTimeSequenceCardSelections, setAcpTimeSequenceCardSelections] =
    useState({});

  const [workflowSetting, setWorkflowSetting] = useState<WorkflowItem[]>([]);
  const [knowledgeSetting, setKnowledgeSetting] = useState<KnowledgeItem[]>([]);
  const [utilitySetting, setUtilitySetting] = useState<UtilityItem[]>([]);
  const [acpServerSetting, setAcpServerSetting] = useState<AcpServerItem[]>([]);
  const [promptTemplateSetting, setPromptTemplateSetting] = useState<any[]>([]);
  const [functionSetting, setFunctionSetting] = useState<any[]>([]);
  const [responseSetting, setResponseSetting] = useState<any[]>([]);
  // 移除 instruction 状态，完全用 form 管理
  // const [instruction, setInstruction] = useState<string>('');
  const [sequenceCardSetting, setSequenceCardSetting] = useState([]);
  const [sequenceCardData, setSequenceCardData] = useState([]);
  const [validationParams, setValidationParams] = useState<
    DataValidationParam[]
  >([]);
  const [agentListFetched, setAgentListFetched] = useState(false);
  const [toolsExpanded, setToolsExpanded] = useState(false);
  const [sequenceCardExpanded, setSequenceCardExpanded] = useState(false);
  const [workflowExpanded, setWorkflowExpanded] = useState(false);
  const [knowledgeExpanded, setKnowledgeExpanded] = useState(false);
  const [acpServerExpanded, setAcpServerExpanded] = useState(false);
  const [functionsExpanded, setFunctionsExpanded] = useState(false);
  const [responsesExpanded, setResponsesExpanded] = useState(false);
  const [promptTemplatesExpanded, setPromptTemplatesExpanded] = useState(false);
  const [acpServerNodeExpanded, setAcpServerNodeExpanded] = useState<{
    [key: string]: boolean;
  }>({});

  // 员工能力弹窗相关 state
  const [abilityModal, setAbilityModal] = useState(false);
  const [selectedAbility, setSelectedAbility] = useState(null);
  const [selectedAbilityName, setSelectedAbilityName] = useState('');

  // 添加分页状态
  const [knowledgePage, setKnowledgePage] = useState(1);
  const [knowledgeHasMore, setKnowledgeHasMore] = useState(true);

  // 1. 打开弹窗时加载知识库 - 使用useCallback避免重复创建
  const fetchKnowledgeList = useCallback(async (searchValue = '', page = 1, append = false) => {
    try {
      setLoadingData((prev) => ({ ...prev, knowledge: true }));
      const res = await fetchKnowledgeCollections({
        keywords: searchValue,
        page: page,
        page_size: 10, 
      });
      
      const rawData = res?.data?.data?.kbs || [];
      const total = res?.data?.data?.total || 0;
      
      const transformedData = rawData.map(item => ({
        ...item,
        title: item.name, // 显示名称
        name: item.name, // 确保有name字段
        description: item.description,
        labels: item.tags || [], // 标签
        createdTime: item.update_time ? new Date(item.update_time).toISOString() : '',
        level: 1,
        key: item.id,
        id: item.id,
        // 额外信息
        doc_num: item.doc_num,
        chunk_num: item.chunk_num || 0,
        creator_name: item.creator_name || '',
        permission: item.permission || 'me',
        status: item.status || '1',
      }));
      
      // 判断是否还有更多数据
      setKnowledgeHasMore(page * 10 < total);
      
      if (append) {
        setKnowledgeData(prev => [...prev, ...transformedData]);
      } else {
        setKnowledgeData(transformedData);
        setKnowledgePage(1); // 重置页码
      }
      
      return transformedData;
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      if (!append) setKnowledgeData([]);
      return [];
    } finally {
      setLoadingData((prev) => ({ ...prev, knowledge: false }));
    }
  }, []);

  // openChooseModal函数定义（恢复）
  const openChooseModal = async (type: string) => {
    try {
      // 检查数据是否正在加载
      if (
        loadingData.workflow ||
        loadingData.knowledge ||
        loadingData.utility
      ) {
        Message.warning('数据加载中，请稍候...');
        return;
      }

      let currentData = [];

      if (type === 'knowledge') {
        if (!knowledgeData || knowledgeData.length === 0) {
          currentData = await fetchKnowledgeList('');
        } else {
          currentData = knowledgeData;
        }
        setModalTitle('添加知识库');
        setModalType('knowledge');
        setCheckedIds(knowledgeSetting.map((item) => item.id));
      }
      const sortTreeData = [...currentData].sort((a, b) => {
        const aChecked = checkedIds.includes(a.id);
        const bChecked = checkedIds.includes(b.id);

        if (aChecked && !bChecked) return -1;
        if (!aChecked && bChecked) return 1;
        return 0;
      });

      setTreeData(sortTreeData);

      setVisibleTreeModal(true);
    } catch (error) {
      console.error('打开模态框失败:', error);
      Message.error('打开失败，请重试');
    }
  };

  // fetchData: 初始化表单（移除知识库初始化加载）
  const fetchData = async () => {
    try {
      if (newEmployeeData) {
        try {
          // 只使用接口返回的数据，不添加默认值
          form.setFieldValue('instruction', newEmployeeData.instruction);
          setSelectedAbilityName(newEmployeeData.name);
          // 设置知识库
          if (newEmployeeData.knowledge_bases) {
            console.log('初始化知识库数据:', newEmployeeData.knowledge_bases);
            const selectedKnowledge = newEmployeeData.knowledge_bases.map(
              (k) => {
                const mappedItem = {
                  id: k.knowledge_id, // 使用 knowledge_id 作为唯一标识
                  name: k.name, // 使用 name 字段
                  type:'document',
                  knowledge_id: k.knowledge_id,
                  source: k.source,
                  disabled: k.disabled,
                  ...k,
                };
                console.log('映射的知识库项:', mappedItem);
                return mappedItem;
              }
            );
            console.log('设置的知识库列表:', selectedKnowledge);
            if (selectedKnowledge.length > 0) {
              setKnowledgeSetting(selectedKnowledge);
            }
          }
        } catch (error) {
          console.error('解析设置信息失败:', error);
        }
        // 设置表单初始值，只使用接口返回的数据
        form.setFieldsValue({
          type: newEmployeeData.type,
          instruction: newEmployeeData.instruction,
          name: newEmployeeData.name,
        });
      } else {
        // 新建模式：不设置任何默认值，完全依赖用户输入
        form.resetFields();
        setSelectedAbilityName('');
        setKnowledgeSetting([]);
      }
    } catch (error) {
      console.error('获取数据失败:', error);
    }
  };

  // useEffect: 初始化表单
  useEffect(() => {
    if (!isInitialized) {
      fetchData();
      setIsInitialized(true);
    }
  }, []);

  // useEffect: 编辑模式切换时重新加载数据
  useEffect(() => {
    if (!isEditing && isInitialized) {
      fetchData();
    }
  }, [isEditing]);

  // useEffect: 监听表单值变化，实时同步到父组件
  // 改为onValuesChange
  const handleFormValuesChange = (changedValues, allValues) => {
    if (onAgentDataUpdate) {
      onAgentDataUpdate({
        instruction: allValues.instruction,
        knowledge_bases: knowledgeSetting, // 确保传递当前的知识库设置 // 确保传递当前的知识库设置
        name: selectedAbilityName,
      });
    }
  };

  // 合并知识库工具函数
  function mergeKnowledgeSetting(oldList, newList) {
    const map = new Map();
    [...oldList, ...newList].forEach((item) => {
      map.set(item.id, item);
    });
    return Array.from(map.values());
  }

  // handleTreeConfirm: 合并知识库
  const handleTreeConfirm = (selectedIds: string[]) => {
    if (!modalType) return;
    const getSelectedItems = (data: any[], ids: string[]) => {
      if (modalType === 'tools') {
        return data.reduce((acc: any[], item) => {
          const toolId = item.id;
          const children = item.children || [];

          if (ids.includes(toolId)) {
            const toolItem = {
              id: toolId,
              name: toolId,
              description: item.description,
              disabled: item.disabled || false,
              functions: children
                .filter((child) => child.type === 'function')
                .map((func) => ({
                  name: func.name,
                })),
              templates: children
                .filter((child) => child.type === 'template')
                .map((template) => ({
                  name: template.name,
                })),
            };

            acc.push(toolItem);
          }
          return acc;
        }, []);
      } else if (modalType === 'acpServer') {
        return data.reduce((acc: any[], item) => {
          // 检查服务器节点是否被选中
          const serverSelected = ids.includes(item.id);

          // 获取被选中的工具节点
          const selectedTools = (item.children || []).filter((tool) => {
            const isIncluded = ids.includes(tool.id);
            return isIncluded;
          });

          // 如果服务器被选中或者有工具被选中
          if (serverSelected || selectedTools.length > 0) {
            const serverItem = {
              id: item.id,
              server_id: item.id,
              name: item.name,
              description: item.description,
              createdTime: item.createdTime,
              ...item,
              // 添加选中的工具信息（保留工具信息但不在UI中显示）
              tools: selectedTools.map((tool) => ({
                name: tool.name,
                description: tool.description,
                artifact_metadata_item_id:
                  tool.timeSequenceCardId ||
                  tool.preSelectedTimeSequenceCard ||
                  '',
              })),
            };

            acc.push(serverItem);
          }

          return acc;
        }, []);
      } else {
        return data.reduce((acc: any[], item) => {
          if (ids.includes(item.id)) {
            acc.push({
              id: item.id,
              name: item.title,
              ...item,
            });
          }
          if (item.children) {
            acc.push(...getSelectedItems(item.children, ids));
          }
          return acc;
        }, []);
      }
    };

    switch (modalType) {
      case 'knowledge':
        const selectedKnowledge = getSelectedItems(knowledgeData, selectedIds);
        setKnowledgeSetting((prev) => {
          const newList = mergeKnowledgeSetting(prev, selectedKnowledge);
          // 立即同步到父组件，转换知识库格式
          if (onAgentDataUpdate) {
            const formattedKnowledges = newList.map(kb => ({
              name: kb.name,
              type: "document",
              disabled: false,
              knowledge_id: kb.id,
              source: "ragflow"
            }));
            onAgentDataUpdate({
              instruction: form.getFieldValue('instruction'),
              knowledge_bases: formattedKnowledges,
              name: selectedAbilityName,
            });
          }
          return newList;
        });
        break;
        // 懒加载使用treeData
        const selectedAcpServer = getSelectedItems(treeData, selectedIds);
        setAcpServerSetting(selectedAcpServer);

        // 初始化或更新展开状态
        setAcpServerNodeExpanded((prev) => {
          const newState = { ...prev };
          selectedAcpServer.forEach((server) => {
            // 如果是新的服务器，默认不展开
            if (!(server.id in newState)) {
              newState[server.id] = false;
            }
          });
          // 移除不再存在的服务器的展开状态
          Object.keys(newState).forEach((serverId) => {
            if (!selectedAcpServer.some((server) => server.id === serverId)) {
              delete newState[serverId];
            }
          });
          return newState;
        });
        break;
    }

    // setVisibleTreeModal(false);
    // setCheckedIds([]);
  };

  const handleModalClose = () => {
    setVisibleTreeModal(false);
    setCheckedIds([]);
    // 关闭模态框时重置数据
    setTreeData([]);
    setModalType('');
  };

  // 初始加载知识库数据 - 使用useCallback避免重复调用
  const handleInitialLoad = useCallback(async () => {
    try {
      const results = await fetchKnowledgeList('', 1, false);
      setTreeData(results);
    } catch (error) {
      console.error('初始加载知识库失败:', error);
    }
  }, []);

  // 加载更多知识库数据 - 使用useCallback避免重复调用
  const handleLoadMore = useCallback(async () => {
    if (!knowledgeHasMore || loadingData.knowledge) return;

    try {
      const nextPage = knowledgePage + 1;
      const results = await fetchKnowledgeList('', nextPage, true);
      setKnowledgePage(nextPage);
      setTreeData(prev => [...prev, ...results]);
    } catch (error) {
      console.error('加载更多知识库失败:', error);
    }
  }, [knowledgeHasMore, loadingData.knowledge, knowledgePage]);

  // 添加搜索处理函数
  const handleSearch = async (
    value: { name: string; label: string },
    type: string
  ) => {
    try {
      if (type === 'knowledge') {
        setLoadingData((prev) => ({ ...prev, knowledge: true }));

        // 重置分页状态
        setKnowledgePage(1);
        setKnowledgeHasMore(true);

        if (!value.name && !value.label) {
          // 如果搜索为空，重新加载第一页
          const searchResults = await fetchKnowledgeList('', 1, false);
          if (Array.isArray(searchResults)) {
            setTreeData(searchResults);
            setKnowledgeData(searchResults); // 同步更新knowledgeData
          }
          return;
        }

        // 执行搜索
        const searchResults = await fetchKnowledgeList(value.name, 1, false);
        if (Array.isArray(searchResults)) {
          // 搜索时不需要额外过滤，API已经处理了关键词搜索
          setTreeData(searchResults);
          // 搜索结果不更新knowledgeData，保持原始数据用于重置
        }
      }
    } catch (error) {
      console.error('搜索出错:', error);
      Message.error({
        content: locale['menu.application.agent.fetch.error'],
      });
    } finally {
      setLoadingData((prev) => ({
        ...prev,
        workflow: false,
        knowledge: false,
        utility: false,
        acpServer: false,
      }));
    }
  };

  // leftContainer: 提示词输入框只用form管理
  const leftContainer = () => {
    return (
      <div className={styles.leftContainer}>
        {/* 提示词 */}
        <RowComponent style={{ marginTop: 16 }}>
          <Text className={styles.subtitle}>提示词</Text>
        </RowComponent>
        <RowComponent style={{ marginTop: 8 }}>
          <Form
            form={form}
            onValuesChange={handleFormValuesChange}
            style={{ width: '100%' }}
          >
            <FormItem
              field="instruction"
              rules={[{ required: true }]}
              validateTrigger={['onBlur', 'onChange']}
              style={{ marginBottom: 0 }}
            >
              <div style={{ position: 'relative', width: '50%' }}>
                <TextArea
                  value={form.getFieldValue('instruction') || ''}
                  placeholder={
                    locale['menu.application.info.basic.placeholder.descript']
                  }
                  maxLength={200}
                  onChange={(e) => {
                    form.setFieldValue('instruction', e.target.value);
                  }}
                  style={{
                    backgroundColor: '#fff',
                    border: '1px solid #e5e6eb',
                    width: '100%',
                    resize: 'none',
                    height: '120px',
                    borderRadius: '8px',
                  }}
                />
                <div
                  style={{
                    position: 'absolute',
                    bottom: '8px',
                    right: '8px',
                    fontSize: '12px',
                    color: 'rgba(0, 0, 0, 0.45)',
                    pointerEvents: 'none',
                  }}
                >
                  {form.getFieldValue('instruction')?.length || 0}/200
                </div>
              </div>
            </FormItem>
          </Form>
        </RowComponent>

        {/* 员工能力 */}
        <RowComponent style={{ marginTop: 24 }}>
          <Text className={styles.subtitle}>员工能力</Text>

          <Button
            type="primary"
            style={{ marginLeft: 12 }}
            onClick={() => setAbilityModal(true)}
            disabled={!isEditing}
          >
            选择员工能力
          </Button>

          {selectedAbilityName && (
            <span style={{ marginLeft: 16, color: '#333' }}>
              当前能力：{selectedAbilityName}
            </span>
          )}
        </RowComponent>

        {/* 知识库 */}
            <RowComponent className={styles.titleRow} style={{ marginTop: 24 }}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.setting.addKnowledge']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {
                    locale[
                      'menu.application.info.setting.placeholder.addKnowledge'
                    ]
                  }
                </Text>
              </div>
              <Button
                className={styles.addApplication}
                onClick={() => openChooseModal('knowledge')}
                disabled={!isEditing}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
              >
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>
            <Col
              span={24}
              style={{ marginBottom: '8px' }}
              className={styles.selectedItemContainer}
            >
              {/* 渲染已选择的知识库 */}
              {knowledgeSetting.length > 0 && (
                <div
                  className={styles.selectedItemList}
                  style={{ position: 'relative' }}
                >
                  {(knowledgeExpanded
                    ? knowledgeSetting
                    : knowledgeSetting.slice(0, 3)
                  ).map((app) => (
                    <Row key={app.id} className={styles.selectedItemRow}>
                      <Col className={styles.selectedItemCol}>
                        <Image
                          src={app.icon_url || WorkflowIcon}
                          width={24}
                          height={24}
                          className={styles.agentIcon}
                        />
                        <Text className={styles.selectedItemText}>
                          {app.name}
                        </Text>
                        <Text className={styles.selectedItemTextContent}>
                          {app.description}
                        </Text>
                        <IconCloseTag
                          className={styles.deleteIcon}
                          style={{
                            cursor: !isEditing ? 'not-allowed' : 'pointer',
                          }}
                          onClick={() => {
                            if (!isEditing) return;
                            console.log('删除知识库:', app);
                            console.log('当前知识库列表:', knowledgeSetting);

                            const updatedKnowledgeSetting = knowledgeSetting.filter(
                              (item) => item.id !== app.id
                            );
                            console.log('删除后知识库列表:', updatedKnowledgeSetting);
                            setKnowledgeSetting(updatedKnowledgeSetting);

                            // 同步更新到父组件，转换知识库格式
                            if (onAgentDataUpdate) {
                              const formattedKnowledges = updatedKnowledgeSetting.map(kb => ({
                                name: kb.name,
                                type: "document",
                                disabled: false,
                                knowledge_id: kb.id,
                                source: "ragflow"
                              }));
                              onAgentDataUpdate({
                                instruction: form.getFieldValue('instruction'),
                                knowledge_bases: formattedKnowledges,
                                name: selectedAbilityName,
                              });
                            }
                          }}
                        />
                      </Col>
                    </Row>
                  ))}

                  {/* 折叠/展开按钮 */}
                  {knowledgeSetting.length > 3 && (
                    <div
                      className={styles.toggleButton}
                      onClick={() => setKnowledgeExpanded(!knowledgeExpanded)}
                    >
                      <div className={styles.toggleArrow}>
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                        >
                          <path
                            d={
                              knowledgeExpanded
                                ? 'M12 10L8 6L4 10'
                                : 'M4 6L8 10L12 6'
                            }
                            stroke="#86909C"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <Text className={styles.toggleText}>
                        {knowledgeExpanded
                          ? '收起'
                          : `展开剩余的 ${
                              knowledgeSetting.length - 3
                            } 个知识库`}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </Col>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.customContainer}>{leftContainer()}</div>

      <TreeModal
        type={modalType}
        title={modalTitle}
        visible={visibleTreeModal}
        onClose={handleModalClose}
        treeData={treeData}
        checkedIds={checkedIds}
        onCheck={setCheckedIds}
        onConfirm={handleTreeConfirm}
        onSearch={(value) => handleSearch(value, modalType)}
        loading={loadingData[modalType === 'tools' ? 'utility' : modalType]}
        agentTimeSequenceCards={sequenceCardSetting}
        acpTimeSequenceCardSelections={acpTimeSequenceCardSelections}
        onInitialLoad={modalType === 'knowledge' ? handleInitialLoad : undefined}
        onLoadMore={modalType === 'knowledge' ? handleLoadMore : undefined}
        hasMore={modalType === 'knowledge' ? knowledgeHasMore : false}
      />
      <AbilitySelectModal
        visible={abilityModal}
        selected={selectedAbility}
        onOk={(item) => {
          setSelectedAbility(item.id);
          setSelectedAbilityName(item.newAbilityName);
          
          // 传递选择的能力的 agent.id 给父组件
          if (onAgentDataUpdate) {
            onAgentDataUpdate({
              agent_id: item.agent?.agent_id, 
              name: item.newAbilityName,
              instruction: form.getFieldValue('instruction'),
              knowledge_bases: knowledgeSetting,
            });
          }
          
          // 自动同步 agent.instruction 到提示词
          if (item.agent && item.agent.instruction) {
            form.setFieldValue('instruction', item.agent.instruction);
          }
          // 自动同步 agent.knowledge_bases 到知识库
          if (item.agent && Array.isArray(item.agent.knowledge_bases)) {
            const mappedKnowledges = item.agent.knowledge_bases.map((kb) => ({
              id: kb.name,
              name: kb.name,
              disabled: false,
              type:'document',
              source:'ragflow'
            }));
            setKnowledgeSetting((prev) => {
              const newList = mergeKnowledgeSetting(prev, mappedKnowledges);
              if (onAgentDataUpdate) {
                onAgentDataUpdate({
                  agent_id: item.agent?.agent_id, // 传递正确的 agent_id
                  instruction: form.getFieldValue('instruction'),
                  knowledge_bases: newList,
                  name: item.newAbilityName,
                });
              }
              return newList;
            });
          }
          setAbilityModal(false);
        }}
        onCancel={() => setAbilityModal(false)}
      />
    </div>
  );
}

export default AiStaffSettings;





